<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'username',
        'password',
        'role',
        'failed_login_attempts',
        'locked_until',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'locked_until' => 'datetime',
        ];
    }

    /**
     * Check if user has admin role
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user has employee role
     */
    public function isEmployee(): bool
    {
        return $this->role === 'employee';
    }

    /**
     * Check if user has user role
     */
    public function isUser(): bool
    {
        return $this->role === 'user';
    }

    /**
     * Legacy method for backward compatibility - check if user has employee role
     *
     * @deprecated Use isEmployee() instead
     */
    public function isNormalUser(): bool
    {
        return $this->role === 'employee';
    }

    /**
     * Legacy method for backward compatibility - check if user has user role
     *
     * @deprecated Use isUser() instead
     */
    public function isClient(): bool
    {
        return $this->role === 'user';
    }

    /**
     * Check if user account is locked
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * Increment failed login attempts
     */
    public function incrementFailedAttempts(): void
    {
        $this->increment('failed_login_attempts');

        if ($this->failed_login_attempts >= 5) {
            $this->update([
                'locked_until' => now()->addMinutes(30),
            ]);
        }
    }

    /**
     * Reset failed login attempts
     */
    public function resetFailedAttempts(): void
    {
        $this->update([
            'failed_login_attempts' => 0,
            'locked_until' => null,
        ]);
    }

    /**
     * Get the bookings for the user
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the bookings made by this user (as admin)
     */
    public function adminBookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'booked_by');
    }

    /**
     * Get the reservations for the user (FPMP Phase 1)
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get upcoming reservations for the user
     */
    public function upcomingReservations(): HasMany
    {
        return $this->reservations()->upcoming()->active();
    }

    /**
     * Get today's reservations for the user
     */
    public function todaysReservations(): HasMany
    {
        return $this->reservations()->today()->active();
    }

    /**
     * Get the user's last login date from sessions table
     */
    public function getLastLoginAttribute(): ?\Carbon\Carbon
    {
        $lastSession = \DB::table('sessions')
            ->where('user_id', $this->id)
            ->orderBy('last_activity', 'desc')
            ->first();

        if ($lastSession) {
            return \Carbon\Carbon::createFromTimestamp($lastSession->last_activity);
        }

        return null;
    }
}
