@extends('layouts.admin')

@section('title', 'User Details - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">User Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $user->name }}</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- User Details Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">User Details</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning btn-sm">
                            <i class="ti ti-edit me-1"></i>Edit User
                        </a>
                        @if ($user->id !== auth()->id())
                            <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal"
                                data-bs-target="#deleteModal">
                                <i class="ti ti-trash me-1"></i>Delete User
                            </button>
                        @endif
                        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Users
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- User Profile Section -->
                        <div class="col-xl-4">
                            <div class="text-center">
                                <span class="avatar avatar-xxl avatar-rounded">
                                    <img src="{{ asset('assets/images/faces/9.jpg') }}" alt="">
                                </span>
                                <div class="mt-3">
                                    <h5 class="fw-semibold mb-1">{{ $user->name }}</h5>
                                    <p class="text-muted mb-1">{{ $user->email }}</p>
                                    @if ($user->role === 'admin')
                                        <span class="badge bg-danger-transparent">Admin</span>
                                    @elseif($user->role === 'employee')
                                        <span class="badge bg-primary-transparent">Employee</span>
                                    @else
                                        <span class="badge bg-success-transparent">User</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="col-xl-4">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Basic Information</div>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Name:</span>
                                            <span>{{ $user->name }}</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Email:</span>
                                            <span>{{ $user->email }}</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Username:</span>
                                            <span>{{ $user->username ?? 'Not set' }}</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Email Verified:</span>
                                            <span>
                                                @if ($user->email_verified_at)
                                                    <span class="badge bg-success-transparent">
                                                        <i class="ri-check-line me-1"></i>Verified
                                                    </span>
                                                @else
                                                    <span class="badge bg-danger-transparent">
                                                        <i class="ri-close-line me-1"></i>Not verified
                                                    </span>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Status -->
                        <div class="col-xl-4">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Account Status</div>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Status:</span>
                                            <span>
                                                @if ($user->isLocked())
                                                    <span class="badge bg-danger-transparent">Locked</span>
                                                @else
                                                    <span class="badge bg-success-transparent">Active</span>
                                                @endif
                                            </span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Last Login:</span>
                                            <span>
                                                @if ($user->last_login)
                                                    {{ $user->last_login->format('M d, Y H:i') }}
                                                @else
                                                    <span class="text-muted">Never</span>
                                                @endif
                                            </span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Created:</span>
                                            <span>{{ $user->created_at->format('M d, Y') }}</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="fw-semibold">Last Updated:</span>
                                            <span>{{ $user->updated_at->format('M d, Y') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Role Update Form -->
                    <div class="row mt-4">
                        <div class="col-xl-6">
                            <div class="card custom-card">
                                <div class="card-header">
                                    <div class="card-title">Update User Role</div>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="{{ route('admin.users.update-role', $user) }}">
                                        @csrf
                                        <div class="row gy-3">
                                            <div class="col-xl-8">
                                                <label for="role" class="form-label">Role</label>
                                                <select name="role" id="role" class="form-select">
                                                    <option value="admin" {{ $user->role === 'admin' ? 'selected' : '' }}>
                                                        Admin</option>
                                                    <option value="employee"
                                                        {{ $user->role === 'employee' ? 'selected' : '' }}>Employee
                                                    </option>
                                                    <!--<option value="user" {{ $user->role === 'user' ? 'selected' : '' }}>
                                                            User</option> -->
                                                </select>
                                            </div>
                                            <div class="col-xl-4 d-flex align-items-end">
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="ti ti-user-check me-1"></i>Update Role
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    @if ($user->id !== auth()->id())
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="deleteModalLabel">Delete User</h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <div class="avatar avatar-xl avatar-rounded bg-danger-transparent mb-3">
                                <i class="ri-delete-bin-line fs-24"></i>
                            </div>
                            <h5 class="text-danger">Are you sure?</h5>
                            <p class="text-muted">
                                Are you sure you want to delete <strong>{{ $user->name }}</strong>
                                ({{ $user->username ?? $user->email }})?
                                This action cannot be undone.
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <form method="POST" action="{{ route('admin.users.destroy', $user) }}" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="ti ti-trash me-1"></i>Delete User
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection
